/**
 * Comprehensive Responsive Design System
 * Mobile-first approach with fluid layouts, flexible media, and CSS Grid/Flexbox
 * Breakpoints: small (≤480px), medium (481–1024px), large (≥1025px)
 */

/* ==========================================================================
   CSS Custom Properties (Design Tokens)
   ========================================================================== */

:root {
  /* Spacing Scale - rem units for scalability */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */

  /* Typography Scale - rem units for accessibility */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Touch Target Sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* ==========================================================================
   Base Styles - Mobile First
   ========================================================================== */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  /* Improve text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Prevent horizontal scroll on mobile */
  overflow-x: hidden;
  
  /* Smooth scrolling for anchor links */
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  width: 100%;
}

/* ==========================================================================
   Typography - Responsive Scaling
   ========================================================================== */

/* Fluid typography using clamp() for smooth scaling */
h1 {
  font-size: clamp(var(--text-2xl), 4vw, var(--text-5xl));
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 var(--space-lg) 0;
}

h2 {
  font-size: clamp(var(--text-xl), 3vw, var(--text-4xl));
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 var(--space-md) 0;
}

h3 {
  font-size: clamp(var(--text-lg), 2.5vw, var(--text-3xl));
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 var(--space-md) 0;
}

h4 {
  font-size: clamp(var(--text-base), 2vw, var(--text-2xl));
  font-weight: 500;
  line-height: 1.4;
  margin: 0 0 var(--space-sm) 0;
}

p {
  font-size: var(--text-base);
  line-height: 1.6;
  margin: 0 0 var(--space-md) 0;
}

/* ==========================================================================
   Responsive Media - Images and Videos
   ========================================================================== */

/* Base responsive media */
img,
video,
svg {
  max-width: 100%;
  height: auto;
  display: block;
}

/* High-DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .responsive-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Responsive picture element */
.responsive-picture {
  display: block;
  width: 100%;
  position: relative;
}

.responsive-picture img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Lazy loading placeholder */
.lazy-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ==========================================================================
   Layout Components - CSS Grid and Flexbox
   ========================================================================== */

/* Container with responsive padding */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Responsive grid system */
.grid {
  display: grid;
  gap: var(--space-md);
  grid-template-columns: 1fr;
}

/* Responsive flex system */
.flex {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.flex-row {
  flex-direction: row;
  flex-wrap: wrap;
}

/* ==========================================================================
   Interactive Elements - Touch-Friendly
   ========================================================================== */

/* Touch-friendly buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: 0.375rem;
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) ease;
  
  /* Ensure text doesn't wrap */
  white-space: nowrap;
  
  /* Remove tap highlight on mobile */
  -webkit-tap-highlight-color: transparent;
}

.btn:hover,
.btn:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

/* Focus styles for accessibility */
.btn:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* ==========================================================================
   Navigation - Responsive and Accessible
   ========================================================================== */

/* Mobile navigation */
.nav-mobile {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background: hsl(var(--background) / 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid hsl(var(--border));
}

.nav-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--touch-target-comfortable);
  height: var(--touch-target-comfortable);
  border: none;
  background: transparent;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.nav-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) ease;
}

.nav-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.nav-link {
  display: block;
  padding: var(--space-md);
  color: hsl(var(--foreground));
  text-decoration: none;
  border-bottom: 1px solid hsl(var(--border));
  transition: background-color var(--duration-fast) ease;
}

.nav-link:hover,
.nav-link:focus {
  background-color: hsl(var(--muted));
}

/* ==========================================================================
   Breakpoint-Specific Styles
   ========================================================================== */

/* Small screens (≤480px) - Mobile */
@media (max-width: 480px) {
  :root {
    --space-md: 0.875rem;
    --space-lg: 1.25rem;
    --space-xl: 1.75rem;
  }
  
  .container {
    padding: 0 var(--space-sm);
  }
  
  .grid {
    gap: var(--space-sm);
  }
  
  .flex {
    gap: var(--space-sm);
  }
}

/* Medium screens (481px - 1024px) - Tablet */
@media (min-width: 481px) and (max-width: 1024px) {
  .container {
    max-width: var(--container-md);
    padding: 0 var(--space-lg);
  }
  
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .flex-row {
    flex-direction: row;
  }
  
  /* Navigation becomes horizontal */
  .nav-menu {
    position: static;
    transform: none;
    opacity: 1;
    visibility: visible;
    display: flex;
    background: transparent;
    border: none;
  }
  
  .nav-toggle {
    display: none;
  }
  
  .nav-link {
    border: none;
    padding: var(--space-sm) var(--space-md);
  }
}

/* Large screens (≥1025px) - Desktop */
@media (min-width: 1025px) {
  .container {
    max-width: var(--container-xl);
    padding: 0 var(--space-xl);
  }
  
  .grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-xl);
  }
  
  .flex {
    gap: var(--space-xl);
  }
  
  /* Enhanced hover effects for desktop */
  .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  /* Desktop navigation enhancements */
  .nav-link:hover {
    background-color: hsl(var(--accent));
  }
}

/* Extra large screens */
@media (min-width: 1400px) {
  .container {
    max-width: var(--container-2xl);
  }
  
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ==========================================================================
   Performance Optimizations
   ========================================================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
  
  .nav-link {
    border-bottom: 2px solid currentColor;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .lazy-loading {
    background: linear-gradient(90deg, #2a2a2a 25%, #1a1a1a 50%, #2a2a2a 75%);
  }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/* Responsive visibility */
.hidden-mobile {
  display: none;
}

.hidden-tablet {
  display: block;
}

.hidden-desktop {
  display: block;
}

@media (min-width: 481px) {
  .hidden-mobile {
    display: block;
  }
  
  .hidden-tablet {
    display: none;
  }
}

@media (min-width: 1025px) {
  .hidden-tablet {
    display: block;
  }
  
  .hidden-desktop {
    display: none;
  }
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for keyboard navigation */
.focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .nav-mobile,
  .btn {
    display: none !important;
  }
  
  .container {
    max-width: none !important;
    padding: 0 !important;
  }
}