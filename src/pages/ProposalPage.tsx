import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { TableOfContents } from "@/components/TableOfContents";
import { ArrowUp, FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import FinancialCharts from "@/components/FinancialCharts";
import financialData from "@/data/financialData.json";
import { ResponsiveNavigation } from "@/components/ResponsiveNavigation";
import { ResponsiveLayout, ResponsiveGrid, ResponsiveFlex, ResponsiveSection } from "@/components/ResponsiveLayout";

// Navigation items for responsive navigation
const navigationItems = [
  { id: "cover", href: "#cover", label: "Cover" },
  { id: "executive-summary", href: "#executive-summary", label: "Executive Summary" },
  { id: "financials", href: "#financials", label: "Financial Plan" },
  { id: "charts", href: "#charts", label: "Charts & Graphs" },
];
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend,
  AreaChart,
  Area,
} from "recharts";

type Section = {
  id: string;
  title: string;
  content: React.ReactNode;
  subsections?: Section[];
};

// Calculate financial projections from the Excel data
function calculateBaseScenarioData() {
  const { assumptions, scenarios } = financialData;
  const data = [];
  
  const years = ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'];
  let revenue = assumptions.revenue_y0;
  
  years.forEach((year, index) => {
    revenue *= (1 + scenarios.base.revenue_growth[index]);
    
    const grossProfit = revenue * scenarios.base.gross_margin[index];
    const sgaExpense = revenue * scenarios.base.sga_pct[index];
    const ebitda = grossProfit - sgaExpense;
    
    const depreciation = assumptions.opening_ppe / assumptions.ppe_useful_life;
    const ebit = ebitda - depreciation;
    const netIncome = Math.max(0, ebit * (1 - assumptions.tax_rate));
    
    // Simplified cash flow calculation
    const cashChange = netIncome + depreciation - (revenue * 0.1); // Simplified working capital impact
    
    data.push({
      year,
      revenue: Math.round(revenue),
      ebitda: Math.round(ebitda),
      netIncome: Math.round(netIncome),
      cashChange: Math.round(cashChange),
    });
  });
  
  return data;
}

const chartData = calculateBaseScenarioData();

const CurrencyTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-md border bg-popover px-3 py-2 text-sm shadow-sm">
        <div className="font-medium">{label}</div>
        {payload.map((p: any) => (
          <div key={p.dataKey} className="flex items-center gap-2">
            <span className="inline-block h-2 w-2 rounded-full" style={{ background: p.color }} />
            <span className="text-muted-foreground">{p.name}:</span>
            <span>${Number(p.value).toLocaleString()}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

const sections: Section[] = [
  {
    id: "cover",
    title: "Business Proposal: The Ice Box, LLC",
    content: (
      <div className="space-y-2">
        <p className="text-muted-foreground">
          A Specialty Retail & Service Destination for the Southern California Hockey Community
        </p>
        <ResponsiveGrid columns={{ default: 1, sm: 2 }} gap="md" className="mt-6">
          <div className="rounded-lg border bg-card p-4 sm:p-6">
            <div className="space-y-2 text-sm sm:text-base">
              <p><span className="font-semibold">Proposal Title:</span> Specialty Retail Ice Hockey Store Proposal</p>
              <p><span className="font-semibold">Company Name:</span> Otter Group, LLC DBA Ice Box Hockey</p>
              <p><span className="font-semibold">Client Name:</span> Prospective Investors & Strategic Partners</p>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4 sm:p-6">
             <div className="space-y-2 text-sm sm:text-base">
               <p><span className="font-semibold">Date of Submission:</span> July 1, 2025</p>
               <p><span className="font-semibold">Prepared by:</span> Morgan Mitrany, Founder and CEO</p>
             </div>
           </div>
         </ResponsiveGrid>
      </div>
    ),
  },
  {
    id: "executive-summary",
    title: "1. Executive Summary",
    content: (
      <div className="space-y-4">
        <p>
          The Ice Box represents a unique opportunity to serve Southern California's underserved hockey community 
          with a specialty retail destination that combines premium equipment, expert services, and authentic 
          community engagement.
        </p>
        <div className="space-y-2">
          <h4 className="font-semibold">Key Highlights:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Targeting a passionate, affluent customer base with limited local options</li>
            <li>Comprehensive product mix: equipment, apparel, accessories, and professional services</li>
            <li>Expert staff with deep hockey knowledge and community connections</li>
            <li>Conservative financial projections showing strong profitability and growth</li>
            <li>Experienced leadership with proven retail and hockey industry expertise</li>
          </ul>
        </div>
        <p>
          With an initial investment of $500,000, The Ice Box is projected to achieve $1.28M in Year 1 revenue 
          with healthy margins and sustainable growth trajectory.
        </p>
      </div>
    ),
  },
  {
    id: "company-description",
    title: "2. Company Description",
    content: (
      <div className="space-y-4">
        <p>
          Otter Group, LLC DBA Ice Box Hockey is a specialty retail company focused on serving the hockey 
          community in Southern California. Our mission is to provide premium equipment, expert services, 
          and authentic community engagement for players at all levels.
        </p>
        <div className="space-y-2">
          <h4 className="font-semibold">Core Values:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><strong>Expertise:</strong> Deep hockey knowledge and professional service standards</li>
            <li><strong>Community:</strong> Building connections within the local hockey ecosystem</li>
            <li><strong>Quality:</strong> Premium products that enhance performance and safety</li>
            <li><strong>Authenticity:</strong> Genuine passion for the sport and its culture</li>
          </ul>
        </div>
        <p>
          Founded by Morgan Mitrany, a lifelong hockey enthusiast with extensive retail and business experience, 
          The Ice Box aims to fill a significant gap in the Southern California market.
        </p>
      </div>
    ),
  },
  {
    id: "market-analysis",
    title: "3. Market Analysis",
    content: (
      <div className="space-y-4">
        <div>
          <h4 className="font-semibold">Market Opportunity</h4>
          <p>
            Southern California's hockey market represents a unique opportunity with high participation rates, 
            affluent demographics, and limited specialized retail options. The region hosts multiple NHL teams, 
            numerous youth leagues, and a growing adult recreational market.
          </p>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Target Demographics:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Youth players (ages 6-18) and their families</li>
            <li>Adult recreational players (ages 18-55)</li>
            <li>Competitive and semi-professional players</li>
            <li>Hockey enthusiasts and collectors</li>
          </ul>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Competitive Landscape:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Limited local specialty hockey retailers</li>
            <li>General sporting goods stores with minimal hockey expertise</li>
            <li>Online retailers lacking personalized service and fitting</li>
            <li>Pro shops at rinks with limited inventory and hours</li>
          </ul>
        </div>
      </div>
    ),
  },
  {
    id: "products-services",
    title: "4. Products & Services",
    content: (
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-semibold">Product Categories:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><strong>Skates:</strong> Premium brands with professional fitting and customization</li>
            <li><strong>Sticks:</strong> Extensive selection with testing area for flex and feel</li>
            <li><strong>Protective Gear:</strong> Complete lines for all positions and skill levels</li>
            <li><strong>Goalie Equipment:</strong> Specialized "Goalie's Corner" with expert consultation</li>
            <li><strong>Apparel:</strong> Team jerseys, practice gear, and lifestyle clothing</li>
            <li><strong>Accessories:</strong> Bags, tape, maintenance supplies, and training aids</li>
          </ul>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Professional Services:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Skate sharpening and profiling with precision equipment</li>
            <li>Stick customization and repair services</li>
            <li>Equipment fitting and sizing consultations</li>
            <li>Gear maintenance and reconditioning</li>
            <li>Team sales and bulk ordering programs</li>
          </ul>
        </div>
        <p>
          Our comprehensive approach ensures customers receive not just products, but complete solutions 
          tailored to their specific needs and playing style.
        </p>
      </div>
    ),
  },
  {
    id: "marketing-sales",
    title: "5. Marketing & Sales Strategy",
    content: (
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-semibold">Marketing Channels:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><strong>Community Engagement:</strong> Partnerships with local rinks, teams, and leagues</li>
            <li><strong>Digital Marketing:</strong> Social media, email campaigns, and online presence</li>
            <li><strong>Event Participation:</strong> Tournaments, trade shows, and hockey events</li>
            <li><strong>Referral Programs:</strong> Incentives for customer and team referrals</li>
            <li><strong>Local Advertising:</strong> Targeted print and radio in hockey-dense areas</li>
          </ul>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Sales Strategy:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Expert consultation and personalized service approach</li>
            <li>Loyalty programs and repeat customer incentives</li>
            <li>Seasonal promotions aligned with hockey calendar</li>
            <li>Team and group sales programs with volume discounts</li>
            <li>Online ordering with in-store pickup and fitting services</li>
          </ul>
        </div>
        <p>
          Our strategy focuses on building long-term relationships within the hockey community rather than 
          transactional sales, creating sustainable competitive advantages.
        </p>
      </div>
    ),
  },
  {
    id: "implementation-ops",
    title: "6. Implementation & Operations",
    content: (
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-semibold">Implementation Timeline:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><strong>Months 1-2:</strong> Secure location, finalize lease, and begin buildout</li>
            <li><strong>Months 3-4:</strong> Complete store construction and equipment installation</li>
            <li><strong>Months 5-6:</strong> Inventory procurement, staff hiring, and training</li>
            <li><strong>Month 7:</strong> Soft opening and community preview events</li>
            <li><strong>Month 8:</strong> Grand opening and full operations launch</li>
          </ul>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Operational Framework:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>3,500 sq ft retail space with optimized layout for customer flow</li>
            <li>Integrated POS system with inventory management and CRM</li>
            <li>Professional service area with specialized equipment</li>
            <li>Efficient supply chain relationships with major manufacturers</li>
            <li>Quality control processes for all products and services</li>
          </ul>
        </div>
        <p>
          Operations will be designed for scalability, with systems and processes that can support 
          future growth and potential additional locations.
        </p>
      </div>
    ),
  },
  {
    id: "org-structure",
    title: "7. Organizational Structure & Management",
    content: (
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-semibold">Leadership Team:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><strong>Morgan Mitrany, Founder & CEO:</strong> Extensive retail and business management experience</li>
            <li><strong>Operations Manager:</strong> Day-to-day store operations and staff management</li>
            <li><strong>Service Technician:</strong> Specialized equipment services and repairs</li>
            <li><strong>Sales Associates:</strong> Customer service and product expertise</li>
          </ul>
        </div>
        <div className="space-y-2">
          <h4 className="font-semibold">Organizational Philosophy:</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Flat organizational structure promoting communication and agility</li>
            <li>Emphasis on hockey knowledge and passion in all hiring decisions</li>
            <li>Continuous training and development programs</li>
            <li>Performance-based compensation and advancement opportunities</li>
            <li>Strong company culture focused on customer service excellence</li>
          </ul>
        </div>
        <p>
          The organization will be built around attracting and retaining team members who share our 
          passion for hockey and commitment to exceptional customer service.
        </p>
      </div>
    ),
  },
  {
    id: "financials",
    title: "8. Financial Plan & Projections",
    content: (
      <div className="space-y-4">
        <div>
          <h4 className="font-semibold">Startup Capitalization & Allocation</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Founder equity: $500,000</li>
            <li>Net PP&E: $240,000</li>
            <li>Initial Inventory: $128,978</li>
            <li>Initial Operating Cash: $181,022</li>
            <li>Prepaid Expenses: $10,000</li>
          </ul>
        </div>
        <div>
          <h4 className="font-semibold">Financial Assumptions (Base)</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li>Revenue: $1,152,555 base; Year 1 growth 11%, then ~8%/yr.</li>
            <li>Gross Margin: ~52%.</li>
            <li>SG&A: 21.5% of revenue in Y1 → 19.5% by Y5.</li>
            <li>CapEx: 3% baseline; up to 4.5% in growth years.</li>
            <li>Working Capital: DSO ~30 days; Inventory Turns ~4.2x; DPO ~45 days.</li>
          </ul>
        </div>
        <div>
          <h4 className="font-semibold">Financial Projections Summary (Base)</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="text-left">
                  <th className="py-2 pr-4">Metric</th>
                  <th className="py-2 pr-4">Year 1</th>
                  <th className="py-2 pr-4">Year 2</th>
                  <th className="py-2 pr-4">Year 3</th>
                  <th className="py-2 pr-4">Year 4</th>
                  <th className="py-2 pr-0">Year 5</th>
                </tr>
              </thead>
              <tbody className="align-top">
                <tr>
                  <td className="py-2 pr-4 font-medium">Revenue</td>
                  <td className="py-2 pr-4">$1,279,336</td>
                  <td className="py-2 pr-4">$1,381,683</td>
                  <td className="py-2 pr-4">$1,464,584</td>
                  <td className="py-2 pr-4">$1,596,396</td>
                  <td className="py-2 pr-0">$1,756,036</td>
                </tr>
                <tr>
                  <td className="py-2 pr-4 font-medium">EBITDA</td>
                  <td className="py-2 pr-4">$223,884</td>
                  <td className="py-2 pr-4">$262,520</td>
                  <td className="py-2 pr-4">$292,917</td>
                  <td className="py-2 pr-4">$319,279</td>
                  <td className="py-2 pr-0">$351,207</td>
                </tr>
                <tr>
                  <td className="py-2 pr-4 font-medium">Net Income</td>
                  <td className="py-2 pr-4">$108,610</td>
                  <td className="py-2 pr-4">$132,155</td>
                  <td className="py-2 pr-4">$153,413</td>
                  <td className="py-2 pr-4">$172,587</td>
                  <td className="py-2 pr-0">$194,237</td>
                </tr>
                <tr>
                  <td className="py-2 pr-4 font-medium">Net Change in Cash</td>
                  <td className="py-2 pr-4">$18,978</td>
                  <td className="py-2 pr-4">$95,889</td>
                  <td className="py-2 pr-4">$146,647</td>
                  <td className="py-2 pr-4">$138,789</td>
                  <td className="py-2 pr-0">$144,498</td>
                </tr>
              </tbody>
            </table>
          </div>
          <p className="text-sm text-muted-foreground mt-2">Note: Projections based on the provided financial model (Base case).</p>
        </div>
      </div>
    ),
  },
  {
    id: "charts",
    title: "9. Charts & Graphs",
    content: (
      <div className="space-y-8">
        {/* Keep the first two summary charts for continuity */}
        <div className="rounded-lg border p-4">
          <h4 className="mb-4 text-sm font-semibold text-muted-foreground">Revenue over 5 Years</h4>
          <div className="h-64 w-full">
            <ResponsiveContainer>
              <LineChart data={chartData} margin={{ left: 8, right: 8, top: 8, bottom: 8 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="year" />
                <YAxis tickFormatter={(v) => `$${(v / 1000).toFixed(0)}k`} />
                <RechartsTooltip content={<CurrencyTooltip />} />
                <Line type="monotone" dataKey="revenue" name="Revenue" stroke="#2563eb" strokeWidth={2} dot={false} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="rounded-lg border p-4">
          <h4 className="mb-4 text-sm font-semibold text-muted-foreground">EBITDA vs Net Income</h4>
          <div className="h-64 w-full">
            <ResponsiveContainer>
              <BarChart data={chartData} margin={{ left: 8, right: 8, top: 8, bottom: 8 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="year" />
                <YAxis tickFormatter={(v) => `$${(v / 1000).toFixed(0)}k`} />
                <Legend />
                <RechartsTooltip content={<CurrencyTooltip />} />
                <Bar dataKey="ebitda" name="EBITDA" fill="#10b981" />
                <Bar dataKey="netIncome" name="Net Income" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Replace the original cash chart block with a full FinancialCharts suite */}
        <FinancialCharts />
      </div>
    ),
  },
  {
    id: "conclusion",
    title: "10. Conclusion",
    content: (
      <p>
        The Ice Box presents a compelling investment opportunity for Southern California’s hockey community. With a
        unique mix of premium products, expert services, and authentic community engagement—and supported by an
        experienced leadership team and conservative financials—the business is positioned for strong performance and
        long-term success.
      </p>
    ),
  },
  {
    id: "appendices",
    title: "11. Appendices",
    content: (
      <div className="space-y-4">
        <ul className="list-disc pl-6 space-y-1">
          <li>Detailed 5-Year Financial Projections (Base, Optimistic, Pessimistic Scenarios)</li>
          <li>Comprehensive Financial Modeling Methodology & Assumptions</li>
          <li>Founder’s Professional Resume</li>
          <li>Supplier Agreements and Testimonials</li>
          <li>Detailed Market Research and Demographic Analysis</li>
          <li>Customer Testimonials from Pilot Initiatives</li>
          <li>Proposed Store Layout and Design Concepts</li>
        </ul>
        <div>
          <h4 className="font-semibold">Design Philosophy</h4>
          <p>
            Modern-industrial aesthetic with polished concrete, exposed ceilings, metal fixtures, and warm wood accents.
            Authentic hockey materials (puck board, stick displays) create a professional, passionate environment.
          </p>
        </div>
        <div>
          <h4 className="font-semibold">Store Layout (3,500 sq. ft.)</h4>
          <ul className="list-disc pl-6 space-y-1">
            <li><span className="font-semibold">Entrance & Power Wall (300 sq. ft.):</span> High-impact feature displays and promotions.</li>
            <li><span className="font-semibold">Stick Zone (500 sq. ft.):</span> Extensive sticks with a small shooting pad to test flex and feel.</li>
            <li><span className="font-semibold">Skate & Fitting Zone (600 sq. ft.):</span> Benches, 3D foot scanner, ovens near service center.</li>
            <li><span className="font-semibold">Protective & Goalie Zone (800 sq. ft.):</span> Dedicated “Goalie’s Corner” and protective gear area.</li>
            <li><span className="font-semibold">Apparel & Accessories (400 sq. ft.):</span> Lifestyle/performance apparel and impulse items.</li>
            <li><span className="font-semibold">Service Center & Checkout (400 sq. ft.):</span> Visible pro equipment and integrated POS/loyalty.</li>
            <li><span className="font-semibold">Community Hub / Lounge (200 sq. ft.):</span> Seating, TV with hockey, community board.</li>
            <li><span className="font-semibold">Stockroom & Office (300 sq. ft.):</span> Inventory and admin space.</li>
          </ul>
        </div>
        <div>
          <h4 className="font-semibold">Technology Integration</h4>
          <p>
            Digital screens for product info and announcements; POS integrated with inventory and CRM for seamless
            experience and analytics.
          </p>
        </div>
      </div>
    ),
  },
];

// TOC items will be auto-generated from sections

function useActiveSection(ids: string[]) {
  const [activeId, setActiveId] = React.useState<string | null>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const visible = entries
          .filter((e) => e.isIntersecting)
          .sort((a, b) => (a.target as HTMLElement).offsetTop - (b.target as HTMLElement).offsetTop);
        if (visible[0]) {
          setActiveId(visible[0].target.id);
        }
      },
      { rootMargin: "0px 0px -70% 0px", threshold: [0, 0.1, 0.5, 1] }
    );

    ids.forEach((id) => {
      const el = document.getElementById(id);
      if (el) observer.observe(el);
    });

    return () => observer.disconnect();
  }, [ids.join(",")]);

  return activeId;
}

const AnchorSection: React.FC<{ id: string; title: string; className?: string; children: React.ReactNode }> = ({
  id,
  title,
  className,
  children,
}) => {
  return (
    <section id={id} className={cn("scroll-mt-24", className)}>
      <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">{title}</h2>
      <div className="mt-3 text-base leading-7 space-y-3">{children}</div>
    </section>
  );
};

const ProposalPage: React.FC = () => {
  const sectionIds = sections.map((section) => section.id);
  const activeId = useActiveSection(sectionIds);

  const handleNavigate = (id: string) => {
    const el = document.getElementById(id);
    if (!el) return;
    el.scrollIntoView({ behavior: "smooth", block: "start" });
    history.replaceState(null, "", `#${id}`);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  React.useEffect(() => {
    const hash = window.location.hash.replace("#", "");
    if (hash) {
      const el = document.getElementById(hash);
      if (el) setTimeout(() => el.scrollIntoView({ behavior: "smooth", block: "start" }), 50);
    }
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Responsive Navigation */}
      <ResponsiveNavigation 
        items={navigationItems} 
        onNavigate={handleNavigate}
        className="border-b"
      />

      {/* Header */}
      <header className="border-b bg-gradient-to-b from-background to-muted/30">
        <ResponsiveContainer className="py-6 sm:py-10 lg:py-14">
          <ResponsiveFlex align="center" gap="md" className="flex-col sm:flex-row">
            <div className="rounded-md bg-primary text-primary-foreground p-2 flex-shrink-0">
              <FileText className="h-6 w-6" />
            </div>
            <div className="text-center sm:text-left">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight">
                The Ice Box, LLC — Specialty Retail Ice Hockey Store
              </h1>
              <p className="mt-2 text-sm text-muted-foreground">
                Otter Group, LLC DBA Ice Box Hockey • Prepared by Morgan Mitrany • July 1, 2025
              </p>
            </div>
          </ResponsiveFlex>
        </ResponsiveContainer>
      </header>

      {/* Main Layout */}
      <ResponsiveLayout variant="sidebar" maxWidth="2xl" className="py-6 sm:py-8 lg:py-10">
        {/* Sidebar - Desktop Only */}
        <aside className="hidden lg:block">
          <div className="sticky top-24 h-fit">
            <div className="space-y-4">
              <div className="rounded-lg border bg-card p-4">
                <h3 className="font-semibold text-sm mb-3">Table of Contents</h3>
                <TableOfContents
                  items={sections.map(section => ({
                    id: section.id,
                    label: section.title,
                    href: `#${section.id}`
                  }))}
                  activeId={activeId}
                  onNavigate={handleNavigate}
                  autoGenerate={false}
                  validateLinks={true}
                  title=""
                />
              </div>
              
              {/* Back to Top Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={scrollToTop}
                className="w-full"
              >
                <ArrowUp className="mr-2 h-4 w-4" />
                Back to Top
              </Button>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="min-w-0">
          <ResponsiveFlex direction="col" gap="lg">
            {sections.map((s, idx) => (
              <ResponsiveSection
                key={s.id}
                id={s.id}
                spacing="md"
                className="scroll-mt-20"
              >
                <AnchorSection 
                  id={s.id} 
                  title={s.title}
                  className="space-y-4 sm:space-y-6"
                >
                  {s.content}
                </AnchorSection>
                {idx < sections.length - 1 && <Separator className="my-6 sm:my-8" />}
              </ResponsiveSection>
            ))}
          </ResponsiveFlex>
        </main>
      </ResponsiveLayout>

      {/* Mobile Back to Top Button */}
      <div className="fixed bottom-4 right-4 lg:hidden z-40">
        <Button
          variant="default"
          size="icon"
          onClick={scrollToTop}
          className="rounded-full shadow-lg min-h-[48px] min-w-[48px] touch-manipulation"
          aria-label="Back to top"
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
};

export default ProposalPage;