import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'sidebar' | 'grid' | 'centered';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
}

interface ResponsiveFlexProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'row' | 'col';
  wrap?: boolean;
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * Responsive layout component with mobile-first approach
 * Implements CSS Grid and Flexbox with consistent spacing system
 * Features:
 * - Mobile-first responsive design
 * - Fluid layouts with proper breakpoints
 * - Consistent spacing using design tokens
 * - Multiple layout variants (default, sidebar, grid, centered)
 * - Accessibility considerations
 */
export function ResponsiveLayout({
  children,
  className,
  variant = 'default',
  spacing = 'md',
  maxWidth = 'xl'
}: ResponsiveLayoutProps) {
  const spacingClasses = {
    none: '',
    sm: 'space-y-2 sm:space-y-3',
    md: 'space-y-4 sm:space-y-6',
    lg: 'space-y-6 sm:space-y-8',
    xl: 'space-y-8 sm:space-y-12'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-4xl',
    '2xl': 'max-w-6xl',
    full: 'max-w-full'
  };

  const variantClasses = {
    default: 'w-full',
    sidebar: cn(
      'w-full lg:grid lg:grid-cols-[250px_1fr] lg:gap-8',
      'xl:grid-cols-[280px_1fr] xl:gap-12'
    ),
    grid: 'w-full',
    centered: 'w-full flex flex-col items-center justify-center min-h-[50vh]'
  };

  return (
    <div
      className={cn(
        'mx-auto px-4 sm:px-6 lg:px-8',
        maxWidthClasses[maxWidth],
        variantClasses[variant],
        spacingClasses[spacing],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Responsive grid component with mobile-first breakpoints
 * Features:
 * - Configurable columns per breakpoint
 * - Consistent gap spacing
 * - Proper alignment options
 * - CSS Grid implementation
 */
export function ResponsiveGrid({
  children,
  className,
  columns = { default: 1, sm: 2, lg: 3 },
  gap = 'md',
  align = 'stretch'
}: ResponsiveGridProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2 sm:gap-3',
    md: 'gap-4 sm:gap-6',
    lg: 'gap-6 sm:gap-8',
    xl: 'gap-8 sm:gap-12'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  // Generate responsive grid column classes
  const getGridCols = () => {
    const classes = [`grid-cols-${columns.default}`];
    
    if (columns.sm) classes.push(`sm:grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
    
    return classes.join(' ');
  };

  return (
    <div
      className={cn(
        'grid w-full',
        getGridCols(),
        gapClasses[gap],
        alignClasses[align],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Responsive flex component with mobile-first approach
 * Features:
 * - Flexible direction and wrapping
 * - Consistent gap spacing
 * - Proper justify and align options
 * - Flexbox implementation
 */
export function ResponsiveFlex({
  children,
  className,
  direction = 'col',
  wrap = false,
  justify = 'start',
  align = 'stretch',
  gap = 'md'
}: ResponsiveFlexProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2 sm:gap-3',
    md: 'gap-4 sm:gap-6',
    lg: 'gap-6 sm:gap-8',
    xl: 'gap-8 sm:gap-12'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
    baseline: 'items-baseline'
  };

  const directionClass = direction === 'row' ? 'flex-row' : 'flex-col';
  const wrapClass = wrap ? 'flex-wrap' : 'flex-nowrap';

  return (
    <div
      className={cn(
        'flex pl-4',
        directionClass,
        wrapClass,
        justifyClasses[justify],
        alignClasses[align],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Responsive container component with consistent padding and max-width
 * Features:
 * - Mobile-first padding system
 * - Configurable max-width constraints
 * - Centered layout with proper margins
 */
export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = true
}: {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean;
}) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-2xl',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <div
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        padding && 'px-4 sm:px-6 lg:px-8',
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Responsive section component with proper semantic structure
 * Features:
 * - Semantic HTML structure
 * - Consistent vertical spacing
 * - Optional background variants
 * - Accessibility considerations
 */
export function ResponsiveSection({
  children,
  className,
  id,
  background = 'default',
  spacing = 'lg'
}: {
  children: React.ReactNode;
  className?: string;
  id?: string;
  background?: 'default' | 'muted' | 'accent';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const spacingClasses = {
    sm: 'py-8 sm:py-12',
    md: 'py-12 sm:py-16',
    lg: 'py-16 sm:py-20',
    xl: 'py-20 sm:py-24'
  };

  const backgroundClasses = {
    default: 'bg-background',
    muted: 'bg-muted/30',
    accent: 'bg-accent/10'
  };

  return (
    <section
      id={id}
      className={cn(
        'w-full',
        spacingClasses[spacing],
        backgroundClasses[background],
        className
      )}
    >
      <ResponsiveContainer>
        {children}
      </ResponsiveContainer>
    </section>
  );
}

export default ResponsiveLayout;