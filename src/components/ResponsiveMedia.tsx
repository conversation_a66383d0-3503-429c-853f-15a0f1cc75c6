import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  lazy?: boolean;
  sizes?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  placeholder?: 'blur' | 'empty';
  onLoad?: () => void;
  onError?: () => void;
}

interface ResponsiveVideoProps {
  src: string;
  poster?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  controls?: boolean;
  lazy?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

interface LazyLoadOptions {
  threshold?: number;
  rootMargin?: string;
}

/**
 * Custom hook for lazy loading with Intersection Observer
 * Features:
 * - Configurable threshold and root margin
 * - Automatic cleanup
 * - Performance optimized
 */
function useLazyLoad(options: LazyLoadOptions = {}) {
  const [isInView, setIsInView] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element || isLoaded) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          setIsLoaded(true);
          observer.unobserve(element);
        }
      },
      {
        threshold: options.threshold || 0.1,
        rootMargin: options.rootMargin || '50px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [isLoaded, options.threshold, options.rootMargin]);

  return { ref, isInView, isLoaded };
}

/**
 * Responsive image component with mobile-first approach
 * Features:
 * - Responsive sizing (max-width: 100%, height: auto)
 * - Lazy loading with Intersection Observer
 * - High-DPI support with srcset
 * - Proper alt text for accessibility
 * - Loading states and error handling
 * - Performance optimized
 */
export function ResponsiveImage({
  src,
  alt,
  className,
  width,
  height,
  priority = false,
  lazy = true,
  sizes = '(max-width: 480px) 100vw, (max-width: 1024px) 50vw, 33vw',
  objectFit = 'cover',
  placeholder = 'empty',
  onLoad,
  onError
}: ResponsiveImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const { ref, isInView } = useLazyLoad({ threshold: 0.1, rootMargin: '50px' });

  const shouldLoad = priority || !lazy || isInView;

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  // Generate srcset for high-DPI displays
  const generateSrcSet = (baseSrc: string) => {
    const extension = baseSrc.split('.').pop();
    const baseName = baseSrc.replace(`.${extension}`, '');
    
    return [
      `${baseSrc} 1x`,
      `${baseName}@2x.${extension} 2x`,
      `${baseName}@3x.${extension} 3x`
    ].join(', ');
  };

  const objectFitClasses = {
    contain: 'object-contain',
    cover: 'object-cover',
    fill: 'object-fill',
    none: 'object-none',
    'scale-down': 'object-scale-down'
  };

  if (hasError) {
    return (
      <div
        ref={ref as React.RefObject<HTMLDivElement>}
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground',
          'min-h-[200px] rounded-md border border-dashed',
          className
        )}
        role="img"
        aria-label={`Failed to load image: ${alt}`}
      >
        <div className="text-center p-4">
          <div className="text-sm font-medium">Image failed to load</div>
          <div className="text-xs mt-1">{alt}</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={cn('relative overflow-hidden', className)}
    >
      {/* Loading placeholder */}
      {isLoading && placeholder === 'blur' && (
        <div
          className={cn(
            'absolute inset-0 bg-muted animate-pulse',
            'flex items-center justify-center'
          )}
          aria-hidden="true"
        >
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      )}

      {/* Main image */}
      {shouldLoad && (
        <img
          src={src}
          srcSet={generateSrcSet(src)}
          sizes={sizes}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'w-full h-auto transition-opacity duration-300',
            objectFitClasses[objectFit],
            isLoading && 'opacity-0',
            !isLoading && 'opacity-100'
          )}
          style={{
            aspectRatio: width && height ? `${width}/${height}` : undefined
          }}
        />
      )}
    </div>
  );
}

/**
 * Responsive video component with mobile-first approach
 * Features:
 * - Responsive sizing (max-width: 100%, height: auto)
 * - Lazy loading with Intersection Observer
 * - Proper controls and accessibility
 * - Loading states and error handling
 * - Performance optimized
 */
export function ResponsiveVideo({
  src,
  poster,
  className,
  autoPlay = false,
  muted = false,
  loop = false,
  controls = true,
  lazy = true,
  onLoad,
  onError
}: ResponsiveVideoProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const { ref, isInView } = useLazyLoad({ threshold: 0.1, rootMargin: '50px' });
  const videoRef = useRef<HTMLVideoElement>(null);

  const shouldLoad = !lazy || isInView;

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  // Handle autoplay with user interaction requirements
  useEffect(() => {
    if (autoPlay && videoRef.current && !isLoading) {
      const playPromise = videoRef.current.play();
      if (playPromise !== undefined) {
        playPromise.catch(() => {
          // Autoplay was prevented, which is expected behavior
          console.log('Autoplay was prevented');
        });
      }
    }
  }, [autoPlay, isLoading]);

  if (hasError) {
    return (
      <div
        ref={ref as React.RefObject<HTMLDivElement>}
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground',
          'min-h-[200px] rounded-md border border-dashed',
          className
        )}
        role="img"
        aria-label="Failed to load video"
      >
        <div className="text-center p-4">
          <div className="text-sm font-medium">Video failed to load</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={cn('relative overflow-hidden rounded-md', className)}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 bg-muted animate-pulse',
            'flex items-center justify-center min-h-[200px]'
          )}
          aria-hidden="true"
        >
          <div className="text-muted-foreground text-sm">Loading video...</div>
        </div>
      )}

      {/* Main video */}
      {shouldLoad && (
        <video
          ref={videoRef}
          poster={poster}
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          controls={controls}
          playsInline
          onLoadedData={handleLoad}
          onError={handleError}
          className={cn(
            'w-full h-auto transition-opacity duration-300',
            isLoading && 'opacity-0',
            !isLoading && 'opacity-100'
          )}
          aria-label="Video content"
        >
          <source src={src} type="video/mp4" />
          <p className="text-muted-foreground p-4">
            Your browser doesn't support video playback.
            <a href={src} className="text-primary hover:underline ml-1">
              Download the video
            </a>
          </p>
        </video>
      )}
    </div>
  );
}

/**
 * Responsive picture component with art direction support
 * Features:
 * - Different images for different breakpoints
 * - WebP format support with fallbacks
 * - High-DPI support
 * - Lazy loading
 */
export function ResponsivePicture({
  sources,
  fallback,
  alt,
  className,
  lazy = true,
  onLoad,
  onError
}: {
  sources: Array<{
    srcSet: string;
    media?: string;
    type?: string;
    sizes?: string;
  }>;
  fallback: {
    src: string;
    width?: number;
    height?: number;
  };
  alt: string;
  className?: string;
  lazy?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const { ref, isInView } = useLazyLoad({ threshold: 0.1, rootMargin: '50px' });

  const shouldLoad = !lazy || isInView;

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  if (hasError) {
    return (
      <div
        ref={ref as React.RefObject<HTMLDivElement>}
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground',
          'min-h-[200px] rounded-md border border-dashed',
          className
        )}
        role="img"
        aria-label={`Failed to load image: ${alt}`}
      >
        <div className="text-center p-4">
          <div className="text-sm font-medium">Image failed to load</div>
          <div className="text-xs mt-1">{alt}</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={cn('relative overflow-hidden', className)}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 bg-muted animate-pulse',
            'flex items-center justify-center'
          )}
          aria-hidden="true"
        >
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      )}

      {/* Picture element with sources */}
      {shouldLoad && (
        <picture>
          {sources.map((source, index) => (
            <source
              key={index}
              srcSet={source.srcSet}
              media={source.media}
              type={source.type}
              sizes={source.sizes}
            />
          ))}
          <img
            src={fallback.src}
            alt={alt}
            width={fallback.width}
            height={fallback.height}
            loading={lazy ? 'lazy' : 'eager'}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            className={cn(
              'w-full h-auto object-cover transition-opacity duration-300',
              isLoading && 'opacity-0',
              !isLoading && 'opacity-100'
            )}
          />
        </picture>
      )}
    </div>
  );
}

export default ResponsiveImage;