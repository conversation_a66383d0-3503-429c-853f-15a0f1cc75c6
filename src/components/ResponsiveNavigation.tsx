import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Menu, X, ChevronDown } from 'lucide-react';

interface NavigationItem {
  id: string;
  label: string;
  href?: string;
  children?: NavigationItem[];
}

interface ResponsiveNavigationProps {
  items: NavigationItem[];
  className?: string;
  onNavigate?: (id: string) => void;
}

/**
 * Responsive navigation component with mobile-first approach
 * Features:
 * - Sticky positioning on mobile
 * - Collapsible mobile menu with hamburger icon
 * - Keyboard-accessible desktop menu
 * - Touch-friendly interactive elements (min 44px hit areas)
 * - Smooth animations and transitions
 * - ARIA labels for accessibility
 */
export function ResponsiveNavigation({ items, className, onNavigate }: ResponsiveNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll for sticky navigation styling
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false);
        setActiveDropdown(null);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const handleItemClick = (id: string) => {
    onNavigate?.(id);
    setIsMobileMenuOpen(false);
    setActiveDropdown(null);
  };

  const toggleDropdown = (id: string) => {
    setActiveDropdown(activeDropdown === id ? null : id);
  };

  const renderNavigationItem = (item: NavigationItem, isMobile = false) => {
    const hasChildren = item.children && item.children.length > 0;
    const isDropdownOpen = activeDropdown === item.id;

    if (hasChildren) {
      return (
        <div key={item.id} className={cn('relative', isMobile && 'w-full')}>
          <button
            onClick={() => toggleDropdown(item.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleDropdown(item.id);
              }
            }}
            className={cn(
              'flex items-center justify-between w-full px-4 py-3 text-left',
              'text-sm font-medium text-foreground hover:text-primary',
              'transition-colors duration-200 focus:outline-none focus:ring-2',
              'focus:ring-primary focus:ring-offset-2 rounded-md',
              'min-h-[44px] touch-manipulation', // Touch-friendly hit area
              isMobile ? 'border-b border-border last:border-b-0' : 'hover:bg-accent'
            )}
            aria-expanded={isDropdownOpen}
            aria-haspopup="true"
          >
            <span>{item.label}</span>
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform duration-200',
                isDropdownOpen && 'rotate-180'
              )}
            />
          </button>
          
          {/* Dropdown Menu */}
          <div
            className={cn(
              'transition-all duration-300 ease-in-out overflow-hidden',
              isMobile
                ? cn(
                    'bg-muted/50',
                    isDropdownOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  )
                : cn(
                    'absolute top-full left-0 mt-1 min-w-[200px] bg-background',
                    'border border-border rounded-md shadow-lg z-50',
                    isDropdownOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
                  )
            )}
          >
            {item.children?.map((child) => (
              <button
                key={child.id}
                onClick={() => handleItemClick(child.id)}
                className={cn(
                  'block w-full px-4 py-3 text-left text-sm text-foreground',
                  'hover:bg-accent hover:text-accent-foreground transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
                  'min-h-[44px] touch-manipulation', // Touch-friendly hit area
                  isMobile ? 'pl-8' : 'rounded-md'
                )}
              >
                {child.label}
              </button>
            ))}
          </div>
        </div>
      );
    }

    return (
      <button
        key={item.id}
        onClick={() => handleItemClick(item.id)}
        className={cn(
          'px-4 py-3 text-sm font-medium text-foreground hover:text-primary',
          'transition-colors duration-200 focus:outline-none focus:ring-2',
          'focus:ring-primary focus:ring-offset-2 rounded-md',
          'min-h-[44px] touch-manipulation', // Touch-friendly hit area
          isMobile ? 'block w-full text-left border-b border-border last:border-b-0' : 'hover:bg-accent'
        )}
      >
        {item.label}
      </button>
    );
  };

  return (
    <nav
      className={cn(
        'sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur',
        'transition-all duration-300',
        isScrolled && 'shadow-sm',
        className
      )}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo/Brand */}
          <div className="flex items-center">
            <h1 className="text-lg font-bold text-primary">Ice Box Hockey</h1>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-1">
            {items.map((item) => renderNavigationItem(item, false))}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden min-h-[44px] min-w-[44px] touch-manipulation"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
            aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </Button>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          id="mobile-menu"
          className={cn(
            'lg:hidden transition-all duration-300 ease-in-out overflow-hidden',
            isMobileMenuOpen
              ? 'max-h-screen opacity-100 pb-4'
              : 'max-h-0 opacity-0'
          )}
          aria-hidden={!isMobileMenuOpen}
        >
          <div className="border-t border-border pt-4">
            {items.map((item) => renderNavigationItem(item, true))}
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm lg:hidden"
          style={{ zIndex: -1 }}
          onClick={() => setIsMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}
    </nav>
  );
}

export default ResponsiveNavigation;