import React from "react";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { ExternalLink, AlertTriangle } from "lucide-react";

type TocItem = {
  id: string;
  label: string;
  depth?: number; // 1 = top, 2 = sub
  href?: string; // Optional custom href
  isExternal?: boolean;
};

interface TableOfContentsProps {
  items: TocItem[];
  activeId?: string | null;
  onNavigate?: (id: string) => void;
  className?: string;
  title?: string;
  autoGenerate?: boolean; // Auto-generate from document headings
  validateLinks?: boolean; // Validate that anchor targets exist
}

/**
 * Utility function to generate URL-safe anchor IDs from text
 * @param text - The heading text to convert
 * @returns URL-safe anchor ID
 */
function generateAnchorId(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Auto-generate TOC items from document headings
 * @param containerSelector - CSS selector for the container to scan
 * @returns Array of TocItem objects
 */
function autoGenerateTocItems(containerSelector: string = 'main'): TocItem[] {
  const container = document.querySelector(containerSelector);
  if (!container) return [];

  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const items: TocItem[] = [];

  headings.forEach((heading) => {
    const level = parseInt(heading.tagName.charAt(1));
    const text = heading.textContent?.trim() || '';
    let id = heading.id;

    // Generate ID if not present
    if (!id) {
      id = generateAnchorId(text);
      heading.id = id;
    }

    items.push({
      id,
      label: text,
      depth: level <= 2 ? 1 : 2, // H1-H2 = depth 1, H3+ = depth 2
    });
  });

  return items;
}

/**
 * Validate that anchor targets exist in the document
 * @param items - Array of TOC items to validate
 * @returns Array of validation results
 */
function validateTocLinks(items: TocItem[]): { id: string; exists: boolean; element?: Element }[] {
  return items.map(item => {
    const element = document.getElementById(item.id);
    return {
      id: item.id,
      exists: !!element,
      element: element || undefined
    };
  });
}

export const TableOfContents: React.FC<TableOfContentsProps> = ({
  items: providedItems,
  activeId,
  onNavigate,
  className,
  title = "Contents",
  autoGenerate = false,
  validateLinks = true,
}) => {
  const [generatedItems, setGeneratedItems] = React.useState<TocItem[]>([]);
  const [linkValidation, setLinkValidation] = React.useState<{ id: string; exists: boolean }[]>([]);
  const [duplicateIds, setDuplicateIds] = React.useState<string[]>([]);

  // Auto-generate items from document headings if enabled
  React.useEffect(() => {
    if (autoGenerate) {
      const items = autoGenerateTocItems();
      setGeneratedItems(items);
    }
  }, [autoGenerate]);

  // Use provided items or auto-generated items
  const items = autoGenerate ? generatedItems : providedItems;

  // Validate links and check for duplicates
  React.useEffect(() => {
    if (validateLinks && items.length > 0) {
      const validation = validateTocLinks(items);
      setLinkValidation(validation);

      // Check for duplicate IDs
      const ids = items.map(item => item.id);
      const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
      setDuplicateIds([...new Set(duplicates)]);
    }
  }, [items, validateLinks]);

  const handleNavigate = React.useCallback((id: string, item: TocItem) => {
    // Check if link exists before navigating
    const validation = linkValidation.find(v => v.id === id);
    if (validateLinks && validation && !validation.exists) {
      console.warn(`TOC: Target element with id "${id}" not found`);
      return;
    }

    // Handle external links
    if (item.isExternal && item.href) {
      window.open(item.href, '_blank', 'noopener,noreferrer');
      return;
    }

    // Handle custom href
    if (item.href) {
      window.location.href = item.href;
      return;
    }

    // Standard anchor navigation
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      // Update URL hash without triggering page reload
      history.replaceState(null, '', `#${id}`);
      // Focus the target element for accessibility
      element.focus({ preventScroll: true });
    }

    onNavigate?.(id);
  }, [linkValidation, validateLinks, onNavigate]);

  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string, item: TocItem) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleNavigate(id, item);
    }
  }, [handleNavigate]);

  return (
    <aside
      className={cn(
        "sticky top-6 hidden h-[calc(100vh-3rem)] w-64 shrink-0 lg:block",
        className
      )}
      aria-label="Table of contents"
      role="navigation"
    >
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-4">
          <h3 className="text-sm font-semibold tracking-wide" id="toc-heading">{title}</h3>
          {duplicateIds.length > 0 && (
            <div className="mt-2 flex items-center gap-1 text-xs text-destructive">
              <AlertTriangle className="h-3 w-3" />
              <span>Duplicate IDs detected</span>
            </div>
          )}
        </div>
        <Separator />
        <ScrollArea className="h-[calc(100vh-8rem)] p-2">
          <nav className="space-y-1" aria-labelledby="toc-heading">
            {items.map((item, index) => {
              const isActive = activeId === item.id;
              const validation = linkValidation.find(v => v.id === item.id);
              const isInvalid = validateLinks && validation && !validation.exists;
              const isDuplicate = duplicateIds.includes(item.id);
              
              return (
                <button
                  key={`${item.id}-${index}`} // Use index to handle duplicates
                  onClick={() => handleNavigate(item.id, item)}
                  onKeyDown={(e) => handleKeyDown(e, item.id, item)}
                  className={cn(
                    "w-full text-left rounded-md px-3 py-2 text-sm transition-colors",
                    "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                    "flex items-center justify-between group",
                    item.depth === 2 ? "ml-2" : "",
                    item.depth === 3 ? "ml-4" : "", // Support for deeper nesting
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "hover:bg-accent hover:text-accent-foreground",
                    isInvalid && "text-destructive hover:text-destructive",
                    isDuplicate && "border border-destructive/50"
                  )}
                  aria-current={isActive ? "page" : undefined}
                  aria-describedby={isInvalid ? `${item.id}-error` : undefined}
                  title={isInvalid ? `Link target not found: ${item.id}` : item.label}
                >
                  <span className="truncate">{item.label}</span>
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {item.isExternal && (
                      <ExternalLink className="h-3 w-3" aria-label="External link" />
                    )}
                    {isInvalid && (
                      <AlertTriangle 
                        className="h-3 w-3 text-destructive" 
                        aria-label="Broken link"
                        id={`${item.id}-error`}
                      />
                    )}
                  </div>
                </button>
              );
            })}
            {items.length === 0 && (
              <div className="px-3 py-2 text-sm text-muted-foreground">
                {autoGenerate ? "No headings found" : "No items provided"}
              </div>
            )}
          </nav>
        </ScrollArea>
      </div>
    </aside>
  );
};