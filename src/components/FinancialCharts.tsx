import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsT<PERSON><PERSON>,
  BarChart,
  Bar,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import { Button } from "@/components/ui/button";
import financialData from "@/data/financialData.json";

type ScenarioData = {
  year: string;
  baseRevenue: number;
  optimisticRevenue: number;
  pessimisticRevenue: number;
  baseEbitda: number;
  optimisticEbitda: number;
  pessimisticEbitda: number;
  baseNetIncome: number;
  optimisticNetIncome: number;
  pessimisticNetIncome: number;
  baseGrossMargin: number;
  optimisticGrossMargin: number;
  pessimisticGrossMargin: number;
};

// Calculate financial projections for all scenarios
function calculateFinancials() {
  const { assumptions, scenarios } = financialData;
  const data: ScenarioData[] = [];
  
  const years = ['Y1', 'Y2', 'Y3', 'Y4', 'Y5'];
  
  // Starting revenue
  let baseRevenue = assumptions.revenue_y0;
  let optRevenue = assumptions.revenue_y0;
  let pessRevenue = assumptions.revenue_y0;
  
  years.forEach((year, index) => {
    // Calculate revenues
    baseRevenue *= (1 + scenarios.base.revenue_growth[index]);
    optRevenue *= (1 + scenarios.optimistic.revenue_growth[index]);
    pessRevenue *= (1 + scenarios.pessimistic.revenue_growth[index]);
    
    // Calculate gross profits
    const baseGrossProfit = baseRevenue * scenarios.base.gross_margin[index];
    const optGrossProfit = optRevenue * scenarios.optimistic.gross_margin[index];
    const pessGrossProfit = pessRevenue * scenarios.pessimistic.gross_margin[index];
    
    // Calculate SG&A expenses
    const baseSGA = baseRevenue * scenarios.base.sga_pct[index];
    const optSGA = optRevenue * scenarios.optimistic.sga_pct[index];
    const pessSGA = pessRevenue * scenarios.pessimistic.sga_pct[index];
    
    // Calculate EBITDA (Gross Profit - SG&A)
    const baseEbitda = baseGrossProfit - baseSGA;
    const optEbitda = optGrossProfit - optSGA;
    const pessEbitda = pessGrossProfit - pessSGA;
    
    // Estimate depreciation (simplified)
    const depreciation = assumptions.opening_ppe / assumptions.ppe_useful_life;
    
    // Calculate Net Income (simplified: EBITDA - Depreciation - Taxes)
    const baseEbit = baseEbitda - depreciation;
    const optEbit = optEbitda - depreciation;
    const pessEbit = pessEbitda - depreciation;
    
    const baseNetIncome = Math.max(0, baseEbit * (1 - assumptions.tax_rate));
    const optNetIncome = Math.max(0, optEbit * (1 - assumptions.tax_rate));
    const pessNetIncome = Math.max(0, pessEbit * (1 - assumptions.tax_rate));
    
    data.push({
      year,
      baseRevenue: Math.round(baseRevenue),
      optimisticRevenue: Math.round(optRevenue),
      pessimisticRevenue: Math.round(pessRevenue),
      baseEbitda: Math.round(baseEbitda),
      optimisticEbitda: Math.round(optEbitda),
      pessimisticEbitda: Math.round(pessEbitda),
      baseNetIncome: Math.round(baseNetIncome),
      optimisticNetIncome: Math.round(optNetIncome),
      pessimisticNetIncome: Math.round(pessNetIncome),
      baseGrossMargin: scenarios.base.gross_margin[index] * 100,
      optimisticGrossMargin: scenarios.optimistic.gross_margin[index] * 100,
      pessimisticGrossMargin: scenarios.pessimistic.gross_margin[index] * 100,
    });
  });
  
  return data;
}

const scenarioData = calculateFinancials();

const CurrencyTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-md border bg-popover px-3 py-2 text-sm shadow-sm">
        <div className="font-medium">{label}</div>
        {payload.map((p: any) => (
          <div key={p.dataKey} className="flex items-center gap-2">
            <span className="inline-block h-2 w-2 rounded-full" style={{ background: p.color }} />
            <span className="text-muted-foreground">{p.name}:</span>
            <span>
              {typeof p.value === "number"
                ? p.dataKey?.toLowerCase().includes("pct")
                  ? `${p.value.toFixed(1)}%`
                  : `$${Number(p.value).toLocaleString()}`
                : p.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export default function FinancialCharts() {
  const [selectedScenario, setSelectedScenario] = useState<'base' | 'optimistic' | 'pessimistic'>('base');

  const getRevenueData = () => {
    return scenarioData.map(item => ({
      year: item.year,
      Base: item.baseRevenue,
      Optimistic: item.optimisticRevenue,
      Pessimistic: item.pessimisticRevenue,
    }));
  };

  const getEbitdaData = () => {
    return scenarioData.map(item => ({
      year: item.year,
      Base: item.baseEbitda,
      Optimistic: item.optimisticEbitda,
      Pessimistic: item.pessimisticEbitda,
    }));
  };

  const getNetIncomeData = () => {
    return scenarioData.map(item => ({
      year: item.year,
      Base: item.baseNetIncome,
      Optimistic: item.optimisticNetIncome,
      Pessimistic: item.pessimisticNetIncome,
    }));
  };

  const getGrossMarginData = () => {
    return scenarioData.map(item => ({
      year: item.year,
      Base: item.baseGrossMargin,
      Optimistic: item.optimisticGrossMargin,
      Pessimistic: item.pessimisticGrossMargin,
    }));
  };

  return (
    <div className="space-y-8">
      {/* Scenario Selector */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Financial Scenarios</h3>
        <div className="flex gap-4 mb-4">
          <Button
            variant={selectedScenario === 'base' ? 'default' : 'outline'}
            onClick={() => setSelectedScenario('base')}
          >
            Base Case
          </Button>
          <Button
            variant={selectedScenario === 'optimistic' ? 'default' : 'outline'}
            onClick={() => setSelectedScenario('optimistic')}
          >
            Optimistic
          </Button>
          <Button
            variant={selectedScenario === 'pessimistic' ? 'default' : 'outline'}
            onClick={() => setSelectedScenario('pessimistic')}
          >
            Pessimistic
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">Revenue Growth (Y1-Y5)</p>
            <p className="text-lg font-semibold">
              {selectedScenario === 'base' && '8% - 10%'}
              {selectedScenario === 'optimistic' && '12% - 15%'}
              {selectedScenario === 'pessimistic' && '5% - 7%'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Gross Margin</p>
            <p className="text-lg font-semibold">
              {selectedScenario === 'base' && '52% - 55%'}
              {selectedScenario === 'optimistic' && '55% - 60%'}
              {selectedScenario === 'pessimistic' && '48% - 52%'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">SG&A % of Revenue</p>
            <p className="text-lg font-semibold">
              {selectedScenario === 'base' && '35% - 40%'}
              {selectedScenario === 'optimistic' && '30% - 35%'}
              {selectedScenario === 'pessimistic' && '40% - 45%'}
            </p>
          </div>
        </div>
      </div>

      {/* Revenue Comparison Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Revenue Projection - All Scenarios</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={getRevenueData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`} />
            <RechartsTooltip content={<CurrencyTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="Base"
              stroke="#2563eb"
              strokeWidth={2}
              dot={{ fill: "#2563eb" }}
            />
            <Line
              type="monotone"
              dataKey="Optimistic"
              stroke="#059669"
              strokeWidth={2}
              dot={{ fill: "#059669" }}
            />
            <Line
              type="monotone"
              dataKey="Pessimistic"
              stroke="#dc2626"
              strokeWidth={2}
              dot={{ fill: "#dc2626" }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* EBITDA Comparison Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">EBITDA Projection - All Scenarios</h3>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={getEbitdaData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`} />
            <RechartsTooltip content={<CurrencyTooltip />} />
            <Legend />
            <Area
              type="monotone"
              dataKey="Pessimistic"
              stackId="1"
              stroke="#dc2626"
              fill="#dc2626"
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="Base"
              stackId="2"
              stroke="#2563eb"
              fill="#2563eb"
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="Optimistic"
              stackId="3"
              stroke="#059669"
              fill="#059669"
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Net Income Comparison Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Net Income Projection - All Scenarios</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={getNetIncomeData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`} />
            <RechartsTooltip content={<CurrencyTooltip />} />
            <Legend />
            <Bar dataKey="Pessimistic" fill="#dc2626" />
            <Bar dataKey="Base" fill="#2563eb" />
            <Bar dataKey="Optimistic" fill="#059669" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Gross Margin Comparison Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Gross Margin % - All Scenarios</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={getGrossMarginData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis tickFormatter={(value) => `${value}%`} />
            <RechartsTooltip
              formatter={(value: number) => [`${value.toFixed(1)}%`, "Gross Margin"]}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="Base"
              stroke="#2563eb"
              strokeWidth={2}
              dot={{ fill: "#2563eb" }}
            />
            <Line
              type="monotone"
              dataKey="Optimistic"
              stroke="#059669"
              strokeWidth={2}
              dot={{ fill: "#059669" }}
            />
            <Line
              type="monotone"
              dataKey="Pessimistic"
              stroke="#dc2626"
              strokeWidth={2}
              dot={{ fill: "#dc2626" }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Key Financial Assumptions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Key Financial Assumptions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">Starting Revenue (Y0)</p>
            <p className="text-lg font-semibold">${(financialData.assumptions.revenue_y0 / 1000000).toFixed(1)}M</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">Tax Rate</p>
            <p className="text-lg font-semibold">{(financialData.assumptions.tax_rate * 100).toFixed(1)}%</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">Opening Cash</p>
            <p className="text-lg font-semibold">${(financialData.assumptions.opening_cash / 1000000).toFixed(1)}M</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">PPE Useful Life</p>
            <p className="text-lg font-semibold">{financialData.assumptions.ppe_useful_life} years</p>
          </div>
        </div>
      </div>
    </div>
  );
}