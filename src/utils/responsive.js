/**
 * Responsive Utilities and Performance Optimizations
 * Demonstrates layout behavior, navigation functionality, and responsive components
 */

// ==========================================================================
// Intersection Observer for Lazy Loading
// ==========================================================================

/**
 * Lazy loading implementation using Intersection Observer API
 * Provides performance optimization by loading images only when needed
 */
export class LazyLoader {
  constructor(options = {}) {
    this.options = {
      rootMargin: '50px 0px',
      threshold: 0.01,
      ...options
    };
    
    this.observer = null;
    this.init();
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        this.options
      );
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target);
        this.observer.unobserve(entry.target);
      }
    });
  }

  loadImage(img) {
    // Add loading class for animation
    img.classList.add('lazy-loading');
    
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;
    
    if (src) {
      img.src = src;
    }
    
    if (srcset) {
      img.srcset = srcset;
    }
    
    img.onload = () => {
      img.classList.remove('lazy-loading');
      img.classList.add('lazy-loaded');
    };
    
    img.onerror = () => {
      img.classList.remove('lazy-loading');
      img.classList.add('lazy-error');
    };
  }

  observe(element) {
    if (this.observer) {
      this.observer.observe(element);
    } else {
      // Fallback for browsers without Intersection Observer
      this.loadImage(element);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// ==========================================================================
// Responsive Navigation Controller
// ==========================================================================

/**
 * Mobile-first responsive navigation with accessibility features
 * Handles mobile menu toggle, keyboard navigation, and focus management
 */
export class ResponsiveNavigation {
  constructor(navElement, options = {}) {
    this.nav = navElement;
    this.options = {
      breakpoint: 1024,
      animationDuration: 300,
      ...options
    };
    
    this.isOpen = false;
    this.isMobile = window.innerWidth < this.options.breakpoint;
    
    this.init();
  }

  init() {
    this.setupElements();
    this.bindEvents();
    this.handleResize();
  }

  setupElements() {
    this.toggle = this.nav.querySelector('[data-nav-toggle]');
    this.menu = this.nav.querySelector('[data-nav-menu]');
    this.links = this.nav.querySelectorAll('[data-nav-link]');
    
    // Set initial ARIA attributes
    if (this.toggle) {
      this.toggle.setAttribute('aria-expanded', 'false');
      this.toggle.setAttribute('aria-controls', this.menu?.id || 'nav-menu');
    }
    
    if (this.menu) {
      this.menu.setAttribute('aria-hidden', 'true');
    }
  }

  bindEvents() {
    // Toggle button click
    if (this.toggle) {
      this.toggle.addEventListener('click', this.toggleMenu.bind(this));
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', this.handleKeydown.bind(this));
    
    // Window resize
    window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
    
    // Click outside to close
    document.addEventListener('click', this.handleOutsideClick.bind(this));
    
    // Link clicks
    this.links.forEach(link => {
      link.addEventListener('click', this.handleLinkClick.bind(this));
    });
  }

  toggleMenu() {
    this.isOpen = !this.isOpen;
    this.updateMenuState();
  }

  openMenu() {
    this.isOpen = true;
    this.updateMenuState();
  }

  closeMenu() {
    this.isOpen = false;
    this.updateMenuState();
  }

  updateMenuState() {
    if (this.toggle) {
      this.toggle.setAttribute('aria-expanded', this.isOpen.toString());
    }
    
    if (this.menu) {
      this.menu.setAttribute('aria-hidden', (!this.isOpen).toString());
      this.menu.classList.toggle('open', this.isOpen);
    }
    
    // Focus management
    if (this.isOpen && this.isMobile) {
      this.focusFirstLink();
    }
  }

  handleKeydown(event) {
    if (!this.isOpen || !this.isMobile) return;
    
    switch (event.key) {
      case 'Escape':
        this.closeMenu();
        this.toggle?.focus();
        break;
      case 'Tab':
        this.handleTabNavigation(event);
        break;
    }
  }

  handleTabNavigation(event) {
    const focusableElements = this.menu.querySelectorAll(
      'a[href], button, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault();
      lastElement.focus();
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault();
      firstElement.focus();
    }
  }

  handleOutsideClick(event) {
    if (!this.isOpen || !this.isMobile) return;
    
    if (!this.nav.contains(event.target)) {
      this.closeMenu();
    }
  }

  handleLinkClick() {
    if (this.isMobile) {
      this.closeMenu();
    }
  }

  handleResize() {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth < this.options.breakpoint;
    
    // Close menu when switching from mobile to desktop
    if (wasMobile && !this.isMobile && this.isOpen) {
      this.closeMenu();
    }
    
    // Update visibility based on screen size
    if (this.toggle) {
      this.toggle.style.display = this.isMobile ? 'flex' : 'none';
    }
  }

  focusFirstLink() {
    const firstLink = this.menu.querySelector('[data-nav-link]');
    if (firstLink) {
      firstLink.focus();
    }
  }

  // Utility function for debouncing resize events
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// ==========================================================================
// Responsive Grid System
// ==========================================================================

/**
 * Dynamic grid system that adapts to screen size and content
 * Provides automatic column calculation and responsive behavior
 */
export class ResponsiveGrid {
  constructor(gridElement, options = {}) {
    this.grid = gridElement;
    this.options = {
      minColumnWidth: 280,
      maxColumns: 4,
      gap: 16,
      ...options
    };
    
    this.init();
  }

  init() {
    this.updateGrid();
    window.addEventListener('resize', this.debounce(this.updateGrid.bind(this), 250));
  }

  updateGrid() {
    const containerWidth = this.grid.offsetWidth;
    const availableWidth = containerWidth - (this.options.gap * (this.options.maxColumns - 1));
    const columns = Math.min(
      this.options.maxColumns,
      Math.max(1, Math.floor(availableWidth / this.options.minColumnWidth))
    );
    
    this.grid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    this.grid.style.gap = `${this.options.gap}px`;
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// ==========================================================================
// Performance Monitor
// ==========================================================================

/**
 * Performance monitoring utilities for responsive design
 * Tracks layout shifts, loading times, and user interactions
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      layoutShifts: [],
      loadTimes: {},
      interactions: []
    };
    
    this.init();
  }

  init() {
    this.observeLayoutShifts();
    this.observeLoadTimes();
    this.observeInteractions();
  }

  observeLayoutShifts() {
    if ('LayoutShift' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            this.metrics.layoutShifts.push({
              value: entry.value,
              timestamp: entry.startTime
            });
          }
        }
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  observeLoadTimes() {
    // Observe resource loading
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.initiatorType === 'img') {
            this.metrics.loadTimes[entry.name] = entry.duration;
          }
        }
      });
      
      observer.observe({ entryTypes: ['resource'] });
    }
  }

  observeInteractions() {
    // Track first input delay
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.interactions.push({
            delay: entry.processingStart - entry.startTime,
            timestamp: entry.startTime
          });
        }
      });
      
      observer.observe({ entryTypes: ['first-input'] });
    }
  }

  getCumulativeLayoutShift() {
    return this.metrics.layoutShifts.reduce((sum, shift) => sum + shift.value, 0);
  }

  getAverageLoadTime() {
    const times = Object.values(this.metrics.loadTimes);
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
  }

  getFirstInputDelay() {
    return this.metrics.interactions.length > 0 ? this.metrics.interactions[0].delay : null;
  }

  getReport() {
    return {
      cumulativeLayoutShift: this.getCumulativeLayoutShift(),
      averageLoadTime: this.getAverageLoadTime(),
      firstInputDelay: this.getFirstInputDelay(),
      totalLayoutShifts: this.metrics.layoutShifts.length,
      totalInteractions: this.metrics.interactions.length
    };
  }
}

// ==========================================================================
// Responsive Image Handler
// ==========================================================================

/**
 * Advanced responsive image handling with srcset and picture elements
 * Supports art direction, high-DPI displays, and format optimization
 */
export class ResponsiveImageHandler {
  constructor() {
    this.supportedFormats = this.detectSupportedFormats();
    this.devicePixelRatio = window.devicePixelRatio || 1;
  }

  detectSupportedFormats() {
    const formats = {};
    
    // Test WebP support
    const webpCanvas = document.createElement('canvas');
    webpCanvas.width = 1;
    webpCanvas.height = 1;
    formats.webp = webpCanvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    // Test AVIF support
    formats.avif = new Promise((resolve) => {
      const avifImage = new Image();
      avifImage.onload = () => resolve(true);
      avifImage.onerror = () => resolve(false);
      avifImage.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
    });
    
    return formats;
  }

  generateSrcSet(basePath, sizes = [320, 640, 1024, 1280, 1920]) {
    return sizes.map(size => {
      const retinaSize = size * this.devicePixelRatio;
      return `${basePath}?w=${retinaSize} ${size}w`;
    }).join(', ');
  }

  createPictureElement(sources, fallback, alt = '') {
    const picture = document.createElement('picture');
    
    // Add source elements for different formats and sizes
    sources.forEach(source => {
      const sourceElement = document.createElement('source');
      sourceElement.media = source.media || '';
      sourceElement.srcset = source.srcset;
      sourceElement.type = source.type || '';
      picture.appendChild(sourceElement);
    });
    
    // Add fallback img element
    const img = document.createElement('img');
    img.src = fallback.src;
    img.alt = alt;
    img.loading = 'lazy';
    img.decoding = 'async';
    
    if (fallback.srcset) {
      img.srcset = fallback.srcset;
    }
    
    picture.appendChild(img);
    
    return picture;
  }

  optimizeForViewport(imagePath, breakpoints = {
    mobile: { maxWidth: 480, quality: 75 },
    tablet: { maxWidth: 1024, quality: 85 },
    desktop: { maxWidth: 1920, quality: 90 }
  }) {
    const currentWidth = window.innerWidth;
    let selectedBreakpoint = breakpoints.desktop;
    
    if (currentWidth <= breakpoints.mobile.maxWidth) {
      selectedBreakpoint = breakpoints.mobile;
    } else if (currentWidth <= breakpoints.tablet.maxWidth) {
      selectedBreakpoint = breakpoints.tablet;
    }
    
    return `${imagePath}?w=${selectedBreakpoint.maxWidth}&q=${selectedBreakpoint.quality}`;
  }
}

// ==========================================================================
// Initialization and Export
// ==========================================================================

/**
 * Initialize all responsive utilities when DOM is ready
 */
export function initializeResponsiveUtils() {
  // Initialize lazy loading
  const lazyLoader = new LazyLoader();
  document.querySelectorAll('img[data-src]').forEach(img => {
    lazyLoader.observe(img);
  });
  
  // Initialize responsive navigation
  const navElement = document.querySelector('[data-responsive-nav]');
  if (navElement) {
    new ResponsiveNavigation(navElement);
  }
  
  // Initialize responsive grids
  document.querySelectorAll('[data-responsive-grid]').forEach(grid => {
    new ResponsiveGrid(grid);
  });
  
  // Initialize performance monitoring
  if (process.env.NODE_ENV === 'development') {
    const monitor = new PerformanceMonitor();
    
    // Log performance report after 5 seconds
    setTimeout(() => {
      console.log('Performance Report:', monitor.getReport());
    }, 5000);
  }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeResponsiveUtils);
} else {
  initializeResponsiveUtils();
}

// Export all utilities
export default {
  LazyLoader,
  ResponsiveNavigation,
  ResponsiveGrid,
  PerformanceMonitor,
  ResponsiveImageHandler,
  initializeResponsiveUtils
};