<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Responsive design demonstration with mobile-first approach">
  <title>Responsive Design Demo - Mobile-First Approach</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="../src/styles/responsive.css" as="style">
  <link rel="preload" href="../src/utils/responsive.js" as="script">
  
  <!-- Critical CSS -->
  <link rel="stylesheet" href="../src/styles/responsive.css">
  
  <!-- Favicon for high-DPI displays -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/png" href="/favicon.png">
  
  <style>
    /* Critical above-the-fold styles */
    .hero {
      min-height: 60vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: var(--space-xl) var(--space-md);
    }
    
    .hero-content h1 {
      font-size: clamp(2rem, 5vw, 4rem);
      margin-bottom: var(--space-md);
      font-weight: 700;
    }
    
    .hero-content p {
      font-size: clamp(1rem, 2.5vw, 1.25rem);
      max-width: 600px;
      margin: 0 auto var(--space-lg) auto;
      opacity: 0.9;
    }
    
    .demo-section {
      padding: var(--space-2xl) 0;
    }
    
    .demo-section:nth-child(even) {
      background-color: #f8fafc;
    }
    
    .card {
      background: white;
      border-radius: 0.5rem;
      padding: var(--space-lg);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform var(--duration-normal) ease, box-shadow var(--duration-normal) ease;
    }
    
    .card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--space-xl);
      margin-top: var(--space-xl);
    }
    
    .image-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: var(--space-md);
      margin-top: var(--space-xl);
    }
    
    .image-item {
      position: relative;
      border-radius: 0.5rem;
      overflow: hidden;
      aspect-ratio: 16/9;
    }
    
    .image-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform var(--duration-normal) ease;
    }
    
    .image-item:hover img {
      transform: scale(1.05);
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--space-lg);
      margin-top: var(--space-xl);
    }
    
    .stat-card {
      text-align: center;
      padding: var(--space-lg);
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .stat-number {
      font-size: clamp(2rem, 4vw, 3rem);
      font-weight: 700;
      color: #667eea;
      display: block;
    }
    
    .stat-label {
      font-size: var(--text-sm);
      color: #64748b;
      margin-top: var(--space-sm);
    }
    
    /* Navigation specific styles */
    .demo-nav {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid #e2e8f0;
      position: sticky;
      top: 0;
      z-index: var(--z-sticky);
    }
    
    .nav-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--space-md);
      max-width: var(--container-xl);
      margin: 0 auto;
    }
    
    .nav-brand {
      font-size: var(--text-xl);
      font-weight: 700;
      color: #1e293b;
      text-decoration: none;
    }
    
    .nav-menu {
      display: none;
      list-style: none;
      margin: 0;
      padding: 0;
    }
    
    .nav-menu.open {
      display: block;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border-bottom: 1px solid #e2e8f0;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .nav-item {
      border-bottom: 1px solid #f1f5f9;
    }
    
    .nav-link {
      display: block;
      padding: var(--space-md);
      color: #475569;
      text-decoration: none;
      transition: background-color var(--duration-fast) ease;
    }
    
    .nav-link:hover,
    .nav-link:focus {
      background-color: #f8fafc;
      color: #1e293b;
    }
    
    .nav-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: var(--touch-target-comfortable);
      height: var(--touch-target-comfortable);
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: 0.25rem;
    }
    
    .nav-toggle:hover {
      background-color: #f1f5f9;
    }
    
    .nav-toggle-icon {
      width: 24px;
      height: 24px;
      position: relative;
    }
    
    .nav-toggle-icon span {
      display: block;
      width: 100%;
      height: 2px;
      background-color: #475569;
      margin: 4px 0;
      transition: all var(--duration-normal) ease;
    }
    
    .nav-toggle.active .nav-toggle-icon span:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active .nav-toggle-icon span:nth-child(2) {
      opacity: 0;
    }
    
    .nav-toggle.active .nav-toggle-icon span:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Responsive breakpoints */
    @media (min-width: 768px) {
      .nav-menu {
        display: flex;
        position: static;
        background: transparent;
        border: none;
        box-shadow: none;
      }
      
      .nav-item {
        border: none;
      }
      
      .nav-link {
        padding: var(--space-sm) var(--space-md);
        border-radius: 0.25rem;
      }
      
      .nav-toggle {
        display: none;
      }
    }
    
    /* Performance optimizations */
    .lazy-loading {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    
    .lazy-loaded {
      animation: fadeIn var(--duration-normal) ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  </style>
</head>
<body>
  <!-- Responsive Navigation -->
  <nav class="demo-nav" data-responsive-nav>
    <div class="nav-container">
      <a href="#" class="nav-brand">ResponsiveDemo</a>
      
      <button class="nav-toggle" data-nav-toggle aria-label="Toggle navigation menu">
        <div class="nav-toggle-icon">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>
      
      <ul class="nav-menu" data-nav-menu id="nav-menu">
        <li class="nav-item">
          <a href="#hero" class="nav-link" data-nav-link>Home</a>
        </li>
        <li class="nav-item">
          <a href="#features" class="nav-link" data-nav-link>Features</a>
        </li>
        <li class="nav-item">
          <a href="#gallery" class="nav-link" data-nav-link>Gallery</a>
        </li>
        <li class="nav-item">
          <a href="#stats" class="nav-link" data-nav-link>Stats</a>
        </li>
        <li class="nav-item">
          <a href="#contact" class="nav-link" data-nav-link>Contact</a>
        </li>
      </ul>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="hero" class="hero">
    <div class="hero-content">
      <h1>Responsive Design Demo</h1>
      <p>Experience mobile-first design with fluid layouts, flexible media, and CSS Grid/Flexbox. This demo showcases responsive breakpoints, touch-friendly interactions, and performance optimizations.</p>
      <button class="btn" style="background: white; color: #667eea; margin-top: var(--space-lg);">
        Explore Features
      </button>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="demo-section">
    <div class="container">
      <h2 style="text-align: center; margin-bottom: var(--space-xl);">Responsive Features</h2>
      
      <div class="feature-grid" data-responsive-grid>
        <div class="card">
          <h3>Mobile-First Approach</h3>
          <p>Built with mobile devices as the primary target, then enhanced for larger screens using progressive enhancement.</p>
          <ul>
            <li>Breakpoints: ≤480px, 481-1024px, ≥1025px</li>
            <li>Touch-friendly interactions (44px+ hit areas)</li>
            <li>Optimized for thumb navigation</li>
          </ul>
        </div>
        
        <div class="card">
          <h3>Fluid Layouts</h3>
          <p>CSS Grid and Flexbox create flexible layouts that adapt seamlessly to any screen size.</p>
          <ul>
            <li>CSS Grid for complex layouts</li>
            <li>Flexbox for component alignment</li>
            <li>Relative units (rem, em, %, vw, vh)</li>
          </ul>
        </div>
        
        <div class="card">
          <h3>Flexible Media</h3>
          <p>Images and videos scale responsively with proper aspect ratios and high-DPI support.</p>
          <ul>
            <li>max-width: 100%; height: auto</li>
            <li>Responsive images with srcset</li>
            <li>Lazy loading for performance</li>
          </ul>
        </div>
        
        <div class="card">
          <h3>Typography Scaling</h3>
          <p>Text scales smoothly across devices using relative units and fluid typography techniques.</p>
          <ul>
            <li>rem/em units for scalability</li>
            <li>clamp() for fluid scaling</li>
            <li>Optimal line heights and spacing</li>
          </ul>
        </div>
        
        <div class="card">
          <h3>Accessibility</h3>
          <p>Built with accessibility in mind, supporting keyboard navigation and screen readers.</p>
          <ul>
            <li>ARIA labels and roles</li>
            <li>Keyboard navigation support</li>
            <li>Focus management</li>
          </ul>
        </div>
        
        <div class="card">
          <h3>Performance</h3>
          <p>Optimized for fast loading with lightweight assets and efficient resource management.</p>
          <ul>
            <li>Lazy loading images</li>
            <li>Minimal blocking resources</li>
            <li>Efficient CSS and JavaScript</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- Image Gallery Section -->
  <section id="gallery" class="demo-section">
    <div class="container">
      <h2 style="text-align: center; margin-bottom: var(--space-xl);">Responsive Image Gallery</h2>
      <p style="text-align: center; color: #64748b; margin-bottom: var(--space-xl);">Images with lazy loading, high-DPI support, and responsive sizing</p>
      
      <div class="image-gallery">
        <!-- Responsive images with lazy loading -->
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=1" 
            data-srcset="https://picsum.photos/400/225?random=1 400w, https://picsum.photos/800/450?random=1 800w"
            alt="Responsive demo image 1"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
        
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=2" 
            data-srcset="https://picsum.photos/400/225?random=2 400w, https://picsum.photos/800/450?random=2 800w"
            alt="Responsive demo image 2"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
        
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=3" 
            data-srcset="https://picsum.photos/400/225?random=3 400w, https://picsum.photos/800/450?random=3 800w"
            alt="Responsive demo image 3"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
        
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=4" 
            data-srcset="https://picsum.photos/400/225?random=4 400w, https://picsum.photos/800/450?random=4 800w"
            alt="Responsive demo image 4"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
        
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=5" 
            data-srcset="https://picsum.photos/400/225?random=5 400w, https://picsum.photos/800/450?random=5 800w"
            alt="Responsive demo image 5"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
        
        <div class="image-item">
          <img 
            data-src="https://picsum.photos/400/225?random=6" 
            data-srcset="https://picsum.photos/400/225?random=6 400w, https://picsum.photos/800/450?random=6 800w"
            alt="Responsive demo image 6"
            loading="lazy"
            class="lazy-loading"
          >
        </div>
      </div>
    </div>
  </section>

  <!-- Statistics Section -->
  <section id="stats" class="demo-section">
    <div class="container">
      <h2 style="text-align: center; margin-bottom: var(--space-xl);">Performance Metrics</h2>
      
      <div class="stats-grid">
        <div class="stat-card">
          <span class="stat-number" data-counter="98">0</span>
          <div class="stat-label">Performance Score</div>
        </div>
        
        <div class="stat-card">
          <span class="stat-number" data-counter="0.1">0</span>
          <div class="stat-label">Cumulative Layout Shift</div>
        </div>
        
        <div class="stat-card">
          <span class="stat-number" data-counter="1.2">0</span>
          <div class="stat-label">Load Time (seconds)</div>
        </div>
        
        <div class="stat-card">
          <span class="stat-number" data-counter="100">0</span>
          <div class="stat-label">Accessibility Score</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="demo-section">
    <div class="container">
      <h2 style="text-align: center; margin-bottom: var(--space-xl);">Responsive Form</h2>
      
      <div style="max-width: 600px; margin: 0 auto;">
        <form class="card">
          <div style="margin-bottom: var(--space-lg);">
            <label for="name" style="display: block; margin-bottom: var(--space-sm); font-weight: 500;">Name</label>
            <input 
              type="text" 
              id="name" 
              name="name" 
              style="width: 100%; padding: var(--space-sm); border: 1px solid #d1d5db; border-radius: 0.25rem; font-size: var(--text-base);"
              required
            >
          </div>
          
          <div style="margin-bottom: var(--space-lg);">
            <label for="email" style="display: block; margin-bottom: var(--space-sm); font-weight: 500;">Email</label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              style="width: 100%; padding: var(--space-sm); border: 1px solid #d1d5db; border-radius: 0.25rem; font-size: var(--text-base);"
              required
            >
          </div>
          
          <div style="margin-bottom: var(--space-lg);">
            <label for="message" style="display: block; margin-bottom: var(--space-sm); font-weight: 500;">Message</label>
            <textarea 
              id="message" 
              name="message" 
              rows="4"
              style="width: 100%; padding: var(--space-sm); border: 1px solid #d1d5db; border-radius: 0.25rem; font-size: var(--text-base); resize: vertical;"
              required
            ></textarea>
          </div>
          
          <button 
            type="submit" 
            class="btn"
            style="background: #667eea; color: white; width: 100%; justify-content: center;"
          >
            Send Message
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer style="background: #1e293b; color: white; padding: var(--space-2xl) 0; text-align: center;">
    <div class="container">
      <p>&copy; 2024 Responsive Design Demo. Built with mobile-first principles.</p>
      <p style="margin-top: var(--space-sm); color: #94a3b8; font-size: var(--text-sm);">Optimized for all devices and screen sizes.</p>
    </div>
  </footer>

  <!-- JavaScript for responsive functionality -->
  <script type="module">
    // Import responsive utilities
    import { 
      LazyLoader, 
      ResponsiveNavigation, 
      ResponsiveGrid,
      PerformanceMonitor 
    } from '../src/utils/responsive.js';

    // Initialize lazy loading
    const lazyLoader = new LazyLoader();
    document.querySelectorAll('img[data-src]').forEach(img => {
      lazyLoader.observe(img);
    });

    // Initialize responsive navigation
    const navElement = document.querySelector('[data-responsive-nav]');
    if (navElement) {
      new ResponsiveNavigation(navElement);
    }

    // Initialize responsive grids
    document.querySelectorAll('[data-responsive-grid]').forEach(grid => {
      new ResponsiveGrid(grid);
    });

    // Initialize performance monitoring
    const monitor = new PerformanceMonitor();

    // Animate counters
    function animateCounters() {
      const counters = document.querySelectorAll('[data-counter]');
      
      counters.forEach(counter => {
        const target = parseFloat(counter.dataset.counter);
        const duration = 2000;
        const start = performance.now();
        
        function updateCounter(currentTime) {
          const elapsed = currentTime - start;
          const progress = Math.min(elapsed / duration, 1);
          
          const current = progress * target;
          counter.textContent = target % 1 === 0 ? Math.floor(current) : current.toFixed(1);
          
          if (progress < 1) {
            requestAnimationFrame(updateCounter);
          }
        }
        
        requestAnimationFrame(updateCounter);
      });
    }

    // Intersection Observer for counter animation
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounters();
          counterObserver.disconnect();
        }
      });
    });

    const statsSection = document.getElementById('stats');
    if (statsSection) {
      counterObserver.observe(statsSection);
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(link.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Log performance metrics after page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        console.log('Performance Report:', monitor.getReport());
      }, 3000);
    });

    // Form submission handler
    document.querySelector('form').addEventListener('submit', (e) => {
      e.preventDefault();
      alert('Form submitted! (This is a demo)');
    });
  </script>

  <!-- Service Worker for caching (optional) -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
</body>
</html>